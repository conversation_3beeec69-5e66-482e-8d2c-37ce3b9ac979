"use client";

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Skeleton } from "@/components/ui/skeleton";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import {
  Star,
  Calendar,
  Folder,
  MoreHorizontal,
  Trash2,
  Search,
  FileText,
  ChevronDown,
  Sparkles,
  StarOff,
} from "lucide-react";
import Link from "next/link";

export default function StarredNotesPage() {
  const { data: session } = useSession();
  const router = useRouter();
  const [notes, setNotes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");

  // Fetch starred notes from API
  const loadStarredNotes = async (showLoader = false) => {
    if (!session) return;

    try {
      if (showLoader) setIsLoading(true);
      const response = await fetch("/api/notes/starred", {
        cache: "no-store",
        headers: {
          "Cache-Control": "no-cache",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch starred notes");
      }

      const data = await response.json();
      setNotes(data.notes || []);
    } catch (error) {
      console.error("Error fetching starred notes:", error);
      setNotes([]);
    } finally {
      if (showLoader) setIsLoading(false);
      setIsInitialLoad(false);
    }
  };

  useEffect(() => {
    loadStarredNotes(true);
  }, [session]);

  // Real-time updates: Refresh when window gains focus (without loader)
  useEffect(() => {
    const handleFocus = () => {
      if (!isInitialLoad) {
        loadStarredNotes(false);
      }
    };

    window.addEventListener("focus", handleFocus);
    return () => window.removeEventListener("focus", handleFocus);
  }, [isInitialLoad]);

  // Real-time updates: Polling every 30 seconds (without loader)
  useEffect(() => {
    if (isInitialLoad) return;

    const interval = setInterval(() => {
      loadStarredNotes(false);
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isInitialLoad]);

  // Real-time updates: Listen for storage events (without loader)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === "notes-updated" && !isInitialLoad) {
        loadStarredNotes(false);
      }
    };

    window.addEventListener("storage", handleStorageChange);
    return () => window.removeEventListener("storage", handleStorageChange);
  }, [isInitialLoad]);

  // Real-time updates: Listen for custom events (immediate updates in same tab)
  useEffect(() => {
    const handleNoteStarredChange = (e) => {
      const { noteId, starred } = e.detail;

      if (starred) {
        // Note was starred - it might appear in starred notes, so refresh
        if (!isInitialLoad) {
          loadStarredNotes(false);
        }
      } else {
        // Note was unstarred - remove it immediately from the list
        setNotes((prev) => prev.filter((note) => note._id !== noteId));
      }
    };

    window.addEventListener("noteStarredChanged", handleNoteStarredChange);
    return () =>
      window.removeEventListener("noteStarredChanged", handleNoteStarredChange);
  }, [isInitialLoad]);

  // Real-time updates: Refresh when page becomes visible (without loader)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && !isInitialLoad) {
        loadStarredNotes(false);
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);
    return () =>
      document.removeEventListener("visibilitychange", handleVisibilityChange);
  }, [isInitialLoad]);

  const handleRemoveFromStarred = async (noteId) => {
    try {
      // Update UI immediately for better UX with smooth transition
      setNotes((prev) => prev.filter((note) => note._id !== noteId));

      // Call API to update starred status
      const response = await fetch(`/api/notes/${noteId}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          starred: false,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to unstar note");
      }

      // Trigger storage event to notify other tabs/pages
      localStorage.setItem("notes-updated", Date.now().toString());

      // Dispatch custom event for immediate updates in same tab
      window.dispatchEvent(
        new CustomEvent("noteStarredChanged", {
          detail: { noteId, starred: false },
        })
      );
    } catch (error) {
      console.error("Failed to remove from starred:", error);
      // Reload notes on error to sync with server (without showing loader)
      loadStarredNotes(false);
    }
  };

  const handleDeleteNote = async (noteId) => {
    if (!confirm("Are you sure you want to delete this note?")) return;

    try {
      // Update UI immediately for better UX with smooth transition
      setNotes((prev) => prev.filter((note) => note._id !== noteId));

      // Call API to delete note
      const response = await fetch(`/api/notes/${noteId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete note");
      }
    } catch (error) {
      console.error("Failed to delete note:", error);
      // Reload notes on error to sync with server (without showing loader)
      loadStarredNotes(false);
    }
  };

  // Filter notes based on search query
  const filteredNotes = notes.filter(
    (note) =>
      note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (note.content &&
        note.content.toLowerCase().includes(searchQuery.toLowerCase()))
  );

  // Format date helper
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now - date) / 1000);

    if (diffInSeconds < 60) return "Just now";
    if (diffInSeconds < 3600)
      return `${Math.floor(diffInSeconds / 60)} minutes ago`;
    if (diffInSeconds < 86400)
      return `${Math.floor(diffInSeconds / 3600)} hours ago`;
    if (diffInSeconds < 604800)
      return `${Math.floor(diffInSeconds / 86400)} days ago`;
    return `${Math.floor(diffInSeconds / 604800)} weeks ago`;
  };

  // Format content helper
  const formatContent = (content) => {
    if (!content) return "";

    let cleanContent = content
      .replace(/<[^>]*>/g, "") // Remove HTML tags
      .replace(/\s+/g, " ") // Replace multiple spaces with single space
      .replace(/\n+/g, " ") // Replace line breaks with spaces
      .trim(); // Remove leading/trailing whitespace

    // Add spaces before capital letters to separate words
    cleanContent = cleanContent.replace(/([a-z])([A-Z])/g, "$1 $2");

    // Limit length and add proper spacing
    return cleanContent.substring(0, 100);
  };

  if (isLoading && isInitialLoad) {
    return (
      <main className="max-w-7xl mx-auto p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
          <div className="relative w-full sm:max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search starred notes..."
              value=""
              className="pl-10 h-10"
              disabled
              readOnly
            />
          </div>
          <Skeleton className="h-10 w-24" />
        </div>

        {/* Compact loading skeleton grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {[...Array(8)].map((_, i) => (
            <Card key={i} className="h-48">
              <CardContent className="p-4 h-full flex flex-col">
                <div className="flex items-start justify-between mb-3 h-4">
                  <Skeleton className="h-4 w-16" />
                  <Skeleton className="h-3.5 w-3.5" />
                </div>
                <div className="flex-1 mb-3 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-1/2" />
                </div>
                <div className="mt-auto pt-3 border-t border-border/30">
                  <Skeleton className="h-3 w-24" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </main>
    );
  }

  return (
    <>
      {/* Main Content */}
      <main className="max-w-7xl mx-auto p-6">
        {/* Enhanced Search and Sort Controls */}
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-8">
          <div className="relative w-full sm:max-w-md">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search starred notes..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 h-10 border-border/60 focus:border-primary/50 transition-colors"
            />
          </div>

          {/* Enhanced Sort Dropdown */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button
                variant="outline"
                size="sm"
                className="h-10 px-4 gap-2 border-border/60 hover:border-primary/50 transition-colors"
              >
                <Calendar className="h-4 w-4" />
                Sort by
                <ChevronDown className="h-3 w-3 opacity-50" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              className="w-48 shadow-lg border-border/60"
            >
              <DropdownMenuItem className="gap-2">
                <Calendar className="h-4 w-4" />
                Recently Updated
              </DropdownMenuItem>
              <DropdownMenuItem className="gap-2">
                <FileText className="h-4 w-4" />
                Title A-Z
              </DropdownMenuItem>
              <DropdownMenuItem className="gap-2">
                <Folder className="h-4 w-4" />
                Folder
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {filteredNotes.length === 0 ? (
          <div className="text-center py-20">
            <div className="flex justify-center mb-8">
              <div className="relative">
                <div className="flex items-center justify-center w-24 h-24 rounded-full bg-gradient-to-br from-accent/10 to-primary/10 border border-accent/20">
                  <Star className="h-12 w-12 text-accent-foreground" />
                </div>
                {!searchQuery && (
                  <div className="absolute -top-2 -right-2 w-8 h-8 bg-gradient-to-br from-yellow-100 to-amber-100 rounded-full flex items-center justify-center border-2 border-background shadow-sm">
                    <Sparkles className="h-4 w-4 text-amber-600" />
                  </div>
                )}
              </div>
            </div>

            <h3 className="text-2xl font-bold mb-4 text-foreground">
              {searchQuery
                ? "No matching starred notes"
                : "✨ Start building your favorites"}
            </h3>

            <p className="text-muted-foreground mb-8 max-w-lg mx-auto leading-relaxed text-base">
              {searchQuery
                ? `No starred notes match "${searchQuery}". Try adjusting your search terms or browse all your starred notes.`
                : "Star your most important notes to create a curated collection of your best ideas. Click the star icon on any note to add it here."}
            </p>

            <div className="flex items-center justify-center gap-4">
              {searchQuery ? (
                <Button
                  variant="outline"
                  onClick={() => setSearchQuery("")}
                  className="gap-2 h-11 px-6"
                >
                  <Star className="h-4 w-4" />
                  Show All Starred
                </Button>
              ) : (
                <>
                  <Button asChild className="gap-2 h-11 px-6">
                    <Link href="/dashboard">
                      <FileText className="h-4 w-4" />
                      Browse Notes
                    </Link>
                  </Button>
                  <Button asChild variant="outline" className="gap-2 h-11 px-6">
                    <Link href="/dashboard/notes/new">
                      <Sparkles className="h-4 w-4" />
                      Create Note
                    </Link>
                  </Button>
                </>
              )}
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 auto-rows-fr">
            {filteredNotes.map((note, index) => (
              <Card
                key={note._id}
                className="group h-48 transition-all duration-200 hover:shadow-md hover:border-primary/20 border-border animate-in fade-in slide-in-from-bottom-4"
                style={{
                  animationDelay: `${index * 50}ms`,
                  animationFillMode: "both",
                }}
              >
                <CardContent className="p-4 h-full flex flex-col">
                  {/* Compact Header */}
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-2 min-h-[16px]">
                      {note.folder && (
                        <Badge
                          variant="secondary"
                          className="text-xs h-4 px-2 bg-muted/10 text-muted-foreground border-muted/50 font-normal"
                        >
                          <Folder className="h-2.5 w-2.5 mr-1" />
                          {note.folder}
                        </Badge>
                      )}
                    </div>

                    {/* Compact Star */}
                    <button
                      onClick={(e) => {
                        e.preventDefault();
                        e.stopPropagation();
                        handleRemoveFromStarred(note._id);
                      }}
                      className="group/star p-1 rounded-full hover:bg-yellow-50 transition-colors duration-200"
                      title="Remove from starred"
                    >
                      <Star className="h-3.5 w-3.5 text-yellow-500 fill-current group-hover/star:text-yellow-600 transition-colors duration-200" />
                    </button>
                  </div>

                  {/* Compact Note Content */}
                  <div className="flex-1 mb-3">
                    <Link
                      href={`/dashboard/notes/${note._id}`}
                      className="block group/content"
                    >
                      <h3 className="font-semibold text-sm mb-2 line-clamp-2 leading-tight text-foreground group-hover/content:text-primary transition-colors duration-200">
                        {note.title}
                      </h3>
                      {note.content && (
                        <p className="text-muted-foreground text-xs line-clamp-2 leading-relaxed">
                          {formatContent(note.content)}
                          {formatContent(note.content).length === 100
                            ? "..."
                            : ""}
                        </p>
                      )}
                    </Link>
                  </div>

                  {/* Compact Footer */}
                  <div className="mt-auto pt-3 border-t border-border/30">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
                        <Calendar className="h-2.5 w-2.5" />
                        <span>Updated {formatDate(note.updatedAt)}</span>
                      </div>

                      {/* Compact Three-dot menu */}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-muted/50 rounded-md"
                          >
                            <MoreHorizontal className="h-3 w-3" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent
                          align="end"
                          className="w-44 shadow-md border bg-background"
                          sideOffset={4}
                        >
                          <DropdownMenuItem
                            asChild
                            className="gap-2 py-2 text-sm"
                          >
                            <Link
                              href={`/dashboard/notes/${note._id}`}
                              className="flex items-center"
                            >
                              <FileText className="h-3.5 w-3.5" />
                              Edit Note
                            </Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleRemoveFromStarred(note._id);
                            }}
                            className="gap-2 py-2 text-sm"
                          >
                            <StarOff className="h-3.5 w-3.5" />
                            Unstar Note
                          </DropdownMenuItem>
                          <DropdownMenuSeparator />
                          <DropdownMenuItem
                            onClick={(e) => {
                              e.stopPropagation();
                              handleDeleteNote(note._id);
                            }}
                            className="gap-2 py-2 text-sm text-destructive focus:text-destructive focus:bg-destructive/10"
                          >
                            <Trash2 className="h-3.5 w-3.5" />
                            Delete Note
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </main>
    </>
  );
}
