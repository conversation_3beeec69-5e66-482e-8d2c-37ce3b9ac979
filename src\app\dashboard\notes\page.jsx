"use client";

import { useState, useEffect, useCallback, useRef } from "react";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbLink,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { Separator } from "@/components/ui/separator";
import {
  Search,
  Plus,
  Grid3X3,
  List,
  Star,
  Calendar,
  Folder,
  MoreHorizontal,
  FileText,
  Trash2,
} from "lucide-react";
import Link from "next/link";

export default function AllNotesPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [notes, setNotes] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isInitialLoad, setIsInitialLoad] = useState(true);
  const [searchQuery, setSearchQuery] = useState("");
  const [sortBy, setSortBy] = useState("updated");
  const [filterBy, setFilterBy] = useState("all");
  const [viewMode, setViewMode] = useState("grid");

  // Ref to prevent double loading
  const hasLoadedRef = useRef(false);

  // Show loading immediately if we're still waiting for session
  const shouldShowLoading =
    status === "loading" || (isLoading && isInitialLoad);

  // Strip HTML tags from content for display with better formatting
  const stripHtml = (html) => {
    if (!html) return "";

    // Create a temporary div element to parse HTML
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = html;

    // Get text content and clean up extra whitespace
    let textContent = tempDiv.textContent || tempDiv.innerText || "";

    // Better text cleaning - preserve word boundaries
    textContent = textContent
      .replace(/\s+/g, " ") // Replace multiple spaces with single space
      .replace(/([a-z])([A-Z])/g, "$1 $2") // Add space between camelCase words
      .replace(/([a-zA-Z])(\d)/g, "$1 $2") // Add space between letters and numbers
      .replace(/(\d)([a-zA-Z])/g, "$1 $2") // Add space between numbers and letters
      .trim();

    return textContent;
  };

  // Loading skeleton component
  const NoteSkeleton = () => (
    <Card className="h-fit">
      <CardContent className="p-4">
        <div className="space-y-3">
          {/* Header skeleton */}
          <div className="flex items-start justify-between mb-3">
            <div className="flex items-center gap-2">
              <Skeleton className="h-5 w-16" /> {/* Badge */}
            </div>
            <div className="flex items-center gap-1">
              <Skeleton className="h-7 w-7 rounded" /> {/* Star button */}
              <Skeleton className="h-7 w-7 rounded" /> {/* Menu button */}
            </div>
          </div>

          {/* Content skeleton */}
          <div className="space-y-2">
            <Skeleton className="h-5 w-3/4" /> {/* Title */}
            <Skeleton className="h-4 w-full" /> {/* Content line */}
            {/* Footer skeleton */}
            <div className="flex items-center justify-between pt-1 border-t border-border/50">
              <Skeleton className="h-3 w-16" /> {/* Date */}
              <Skeleton className="h-3 w-12" /> {/* Word count */}
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const loadNotes = useCallback(
    async (showLoader = true) => {
      if (!session) return;

      try {
        if (showLoader) setIsLoading(true);

        const response = await fetch("/api/notes", {
          cache: "no-store",
          headers: {
            "Cache-Control": "no-cache",
          },
        });

        if (!response.ok) {
          throw new Error("Failed to fetch notes");
        }

        const data = await response.json();

        // Transform API data to match component expectations
        const transformedNotes = data.notes.map((note) => ({
          id: note._id,
          title: note.title,
          content: note.content,
          folder: note.folder,
          starred: note.starred,
          createdAt: new Date(note.createdAt),
          updatedAt: new Date(note.updatedAt),
        }));

        setNotes(transformedNotes);
      } catch (error) {
        console.error("Failed to load notes:", error);
        if (showLoader) toast.error("Failed to load notes");
        setNotes([]);
      } finally {
        if (showLoader) setIsLoading(false);
        setIsInitialLoad(false);
      }
    },
    [session]
  );

  useEffect(() => {
    // Only run once when session status changes from loading to loaded
    if (status === "loading") return;

    if (
      status === "authenticated" &&
      session &&
      !hasLoadedRef.current &&
      isInitialLoad
    ) {
      hasLoadedRef.current = true;
      loadNotes();
    } else if (status === "unauthenticated") {
      setIsLoading(false);
      setIsInitialLoad(false);
    }
  }, [status, session, loadNotes]); // Minimal dependencies

  const handleDeleteNote = async (noteId) => {
    if (!confirm("Are you sure you want to delete this note?")) return;

    try {
      const response = await fetch(`/api/notes/${noteId}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        throw new Error("Failed to delete note");
      }

      // Remove from local state
      setNotes((prev) => prev.filter((note) => note.id !== noteId));
      toast.success("Note moved to trash");
    } catch (error) {
      console.error("Failed to delete note:", error);
      toast.error("Failed to delete note");
    }
  };

  const handleStarToggle = async (noteId) => {
    try {
      const note = notes.find((n) => n.id === noteId);
      if (!note) return;

      const response = await fetch(`/api/notes/${noteId}`, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          starred: !note.starred,
        }),
      });

      if (!response.ok) {
        throw new Error("Failed to update note");
      }

      // Update local state
      setNotes((prev) =>
        prev.map((n) => (n.id === noteId ? { ...n, starred: !n.starred } : n))
      );

      toast.success(note.starred ? "Removed from starred" : "Added to starred");
    } catch (error) {
      console.error("Failed to toggle star:", error);
      toast.error("Failed to update note");
    }
  };

  const filteredNotes = notes
    .filter((note) => {
      const matchesSearch =
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase());
      const matchesFilter =
        filterBy === "all" ||
        (filterBy === "starred" && note.starred) ||
        (filterBy === "folder" && note.folder === filterBy);
      return matchesSearch && matchesFilter;
    })
    .sort((a, b) => {
      switch (sortBy) {
        case "title":
          return a.title.localeCompare(b.title);
        case "created":
          return new Date(b.createdAt) - new Date(a.createdAt);
        case "updated":
        default:
          return new Date(b.updatedAt) - new Date(a.updatedAt);
      }
    });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-96">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="flex-1 flex flex-col h-full">
      {/* Header with controls */}
      <div className="flex-shrink-0 bg-background border-b border-border">
        <div className="max-w-7xl mx-auto px-6 py-4">
          {/* Header Content */}
          <div className="flex items-center justify-between mb-4">
            <div>
              <h1 className="text-xl font-semibold text-foreground">Notes</h1>
              <p className="text-sm text-muted-foreground">
                {shouldShowLoading ? (
                  <Skeleton className="h-4 w-16" />
                ) : (
                  <>
                    {filteredNotes.length}{" "}
                    {filteredNotes.length === 1 ? "note" : "notes"}
                  </>
                )}
              </p>
            </div>
            <Button asChild size="sm" className="gap-2">
              <Link href="/dashboard">
                <Plus className="h-4 w-4" />
                New Note
              </Link>
            </Button>
          </div>

          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-3 items-start sm:items-center justify-between">
            {/* Search */}
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                placeholder="Search notes..."
                className="pl-10 h-9"
                disabled={shouldShowLoading}
              />
            </div>

            {/* Filter Controls */}
            <div className="flex items-center gap-2">
              <Button
                variant={filterBy === "all" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterBy("all")}
                className="h-8"
              >
                All Notes
              </Button>
              <Button
                variant={filterBy === "starred" ? "default" : "outline"}
                size="sm"
                onClick={() => setFilterBy("starred")}
                className="h-8"
              >
                <Star className="h-3 w-3 mr-1" />
                Starred
              </Button>

              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-36 h-8">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="updated">Last Updated</SelectItem>
                  <SelectItem value="created">Date Created</SelectItem>
                  <SelectItem value="title">Title</SelectItem>
                </SelectContent>
              </Select>

              <div className="flex items-center border rounded-md">
                <Button
                  variant={viewMode === "grid" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("grid")}
                  className="rounded-r-none h-8 w-8 p-0"
                >
                  <Grid3X3 className="h-3 w-3" />
                </Button>
                <Button
                  variant={viewMode === "list" ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setViewMode("list")}
                  className="rounded-l-none h-8 w-8 p-0"
                >
                  <List className="h-3 w-3" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Notes Content */}
      <main className="flex-1 overflow-auto">
        <div className="max-w-7xl mx-auto px-6 py-4">
          {/* Show loading skeletons during initial load */}
          {shouldShowLoading ? (
            <div
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
                  : "space-y-3"
              }
            >
              {Array.from({ length: 8 }).map((_, index) => (
                <NoteSkeleton key={index} />
              ))}
            </div>
          ) : filteredNotes.length === 0 ? (
            <div className="text-center py-8">
              <div className="bg-muted/30 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                <FileText className="h-8 w-8 text-muted-foreground" />
              </div>
              <h3 className="text-base font-medium mb-2">
                {searchQuery ? "No notes found" : "No notes yet"}
              </h3>
              <p className="text-muted-foreground mb-4 max-w-sm mx-auto text-sm">
                {searchQuery
                  ? "Try adjusting your search terms"
                  : "Create your first note to get started"}
              </p>
              <Button asChild size="sm">
                <Link href="/dashboard">
                  <Plus className="h-4 w-4 mr-2" />
                  Create Note
                </Link>
              </Button>
            </div>
          ) : (
            <div
              className={
                viewMode === "grid"
                  ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4"
                  : "space-y-3"
              }
            >
              {filteredNotes.map((note) => (
                <Card
                  key={note.id}
                  className="group cursor-pointer transition-all duration-200 hover:shadow-md h-fit"
                  onClick={() => router.push(`/dashboard/notes/${note.id}`)}
                >
                  <CardContent className="p-4">
                    {/* Header */}
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-2">
                        {note.folder && (
                          <Badge variant="secondary" className="text-xs h-5">
                            <Folder className="h-3 w-3 mr-1" />
                            {note.folder}
                          </Badge>
                        )}
                        {note.starred && (
                          <Star className="h-3 w-3 text-yellow-500 fill-yellow-500" />
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation();
                            handleStarToggle(note.id);
                          }}
                          className="h-7 w-7 p-0"
                        >
                          <Star
                            className={`h-3 w-3 ${
                              note.starred
                                ? "text-yellow-500 fill-yellow-500"
                                : "text-muted-foreground"
                            }`}
                          />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={(e) => e.stopPropagation()}
                              className="h-7 w-7 p-0"
                            >
                              <MoreHorizontal className="h-3 w-3" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem asChild>
                              <Link href={`/dashboard/notes/${note.id}`}>
                                <FileText className="h-4 w-4 mr-2" />
                                Edit
                              </Link>
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleStarToggle(note.id);
                              }}
                            >
                              <Star className="h-4 w-4 mr-2" />
                              {note.starred ? "Unstar" : "Star"}
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteNote(note.id);
                              }}
                              className="text-destructive"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="space-y-2">
                      <h3 className="font-semibold text-base line-clamp-2 leading-tight">
                        {note.title || "Untitled Note"}
                      </h3>
                      <p className="text-muted-foreground text-sm line-clamp-1 leading-relaxed">
                        {note.content
                          ? (() => {
                              const cleanContent = stripHtml(note.content);
                              if (!cleanContent || cleanContent.trim() === "") {
                                return "No content";
                              }

                              // Show only first line with ellipsis
                              const firstLine = cleanContent
                                .split("\n")[0]
                                .trim();
                              const words = firstLine.split(" ");

                              // Limit to reasonable number of words for first line
                              if (words.length > 8) {
                                return words.slice(0, 8).join(" ") + "...";
                              }

                              // Add ellipsis if there's more content beyond first line
                              return cleanContent.includes("\n") ||
                                cleanContent.length > firstLine.length
                                ? firstLine + "..."
                                : firstLine;
                            })()
                          : "No content"}
                      </p>
                      <div className="flex items-center justify-between text-xs text-muted-foreground pt-1 border-t border-border/50">
                        <div className="flex items-center">
                          <Calendar className="h-3 w-3 mr-1" />
                          {new Date(note.updatedAt).toLocaleDateString(
                            "en-US",
                            {
                              month: "short",
                              day: "numeric",
                              year:
                                new Date(note.updatedAt).getFullYear() !==
                                new Date().getFullYear()
                                  ? "numeric"
                                  : undefined,
                            }
                          )}
                        </div>
                        {note.content && (
                          <div className="text-xs text-muted-foreground/70">
                            {stripHtml(note.content).split(" ").length} words
                          </div>
                        )}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
