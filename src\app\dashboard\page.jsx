"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useNotes } from "@/contexts/NotesContext";
import { SidebarTrigger } from "@/components/ui/sidebar";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbList,
  BreadcrumbPage,
} from "@/components/ui/breadcrumb";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Send,
  FileText,
  Search,
  Star,
  BookOpen,
  Tag,
  Brain,
} from "lucide-react";

export default function DashboardHomePage() {
  const router = useRouter();
  const { triggerRefresh } = useNotes();

  // Note creation state
  const [noteTitle, setNoteTitle] = useState("");
  const [isCreating, setIsCreating] = useState(false);

  // Handle note creation
  const handleCreateNote = async () => {
    if (!noteTitle.trim()) return;

    setIsCreating(true);
    try {
      // Create note via API
      const response = await fetch("/api/notes", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          title: noteTitle.trim(),
          content: "", // Start with empty content
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create note");
      }

      const data = await response.json();
      const createdNote = data.note;

      // Show success message
      toast.success("Note created successfully!");

      // Clear the input
      setNoteTitle("");

      // Refresh sidebar notes
      triggerRefresh();

      // Redirect to the created note
      router.push(`/dashboard/notes/${createdNote._id}`);
    } catch (error) {
      console.error("Failed to create note:", error);
      toast.error(error.message || "Failed to create note. Please try again.");
    } finally {
      setIsCreating(false);
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleCreateNote();
    }
  };

  return (
    <div className="relative h-full flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 overflow-hidden">
      {/* Subtle Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 pointer-events-none" />
      <div className="absolute top-1/4 left-1/4 w-32 h-32 bg-primary/10 rounded-full blur-3xl animate-pulse pointer-events-none" />
      <div className="absolute bottom-1/3 right-1/4 w-24 h-24 bg-accent/10 rounded-full blur-2xl animate-pulse delay-1000 pointer-events-none" />

      <div className="relative z-10 w-full max-w-xs sm:max-w-md md:max-w-lg lg:max-w-2xl mx-auto space-y-8 sm:space-y-10">
        {/* Enhanced Greeting Header */}
        <div className="text-center space-y-3 sm:space-y-4">
          <div className="flex items-center justify-center gap-3 sm:gap-4">
            <div className="relative">
              <Brain className="h-10 w-10 sm:h-12 sm:w-12 text-primary animate-pulse" />
              <div className="absolute inset-0 h-10 w-10 sm:h-12 sm:w-12 bg-primary/20 rounded-full blur-lg animate-ping" />
            </div>
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold bg-gradient-to-r from-foreground via-primary to-foreground/80 bg-clip-text text-transparent">
              SmartNotes
            </h1>
          </div>
          <p className="text-base sm:text-lg md:text-xl text-muted-foreground/80 font-medium">
            What would you like to create today?
          </p>
        </div>

        {/* Enhanced Note Creation Input */}
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 to-accent/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
          <div className="relative bg-background/80 backdrop-blur-sm rounded-2xl border-2 border-border/50 hover:border-primary/30 focus-within:border-primary/50 transition-all duration-300 shadow-lg hover:shadow-xl">
            <Input
              value={noteTitle}
              onChange={(e) => setNoteTitle(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Enter a title for your note..."
              className="w-full h-14 sm:h-16 md:h-18 text-base sm:text-lg md:text-xl pr-14 sm:pr-16 md:pr-18 bg-transparent border-0 focus-visible:ring-0 placeholder:text-muted-foreground/60 font-medium"
              disabled={isCreating}
            />
            <Button
              onClick={handleCreateNote}
              disabled={!noteTitle.trim() || isCreating}
              size="icon"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-10 w-10 sm:h-12 sm:w-12 md:h-14 md:w-14 rounded-xl transition-all duration-300 hover:scale-110 disabled:hover:scale-100 shadow-lg hover:shadow-xl disabled:opacity-50"
            >
              {isCreating ? (
                <div className="animate-spin rounded-full h-5 w-5 sm:h-6 sm:w-6 md:h-7 md:w-7 border-b-2 border-white"></div>
              ) : (
                <Send className="h-5 w-5 sm:h-6 sm:w-6 md:h-7 md:w-7 transition-transform group-hover:translate-x-0.5" />
              )}
            </Button>
          </div>
        </div>

        {/* Enhanced Quick Action Cards */}
        <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-5 gap-3 sm:gap-4">
          <div
            onClick={() => router.push("/dashboard/notes")}
            className="group relative bg-background/60 backdrop-blur-sm border border-border/50 rounded-xl p-4 sm:p-5 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl hover:border-primary/30 hover:bg-primary/5"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-blue-500/10 to-blue-600/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-2 sm:gap-3">
              <div className="p-2 sm:p-3 bg-blue-500/10 rounded-lg group-hover:bg-blue-500/20 transition-colors duration-300">
                <FileText className="h-5 w-5 sm:h-6 sm:w-6 text-blue-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xs sm:text-sm font-medium text-center group-hover:text-blue-600 transition-colors duration-300">
                All Notes
              </span>
            </div>
          </div>

          <div
            onClick={() => router.push("/dashboard/notes/starred")}
            className="group relative bg-background/60 backdrop-blur-sm border border-border/50 rounded-xl p-4 sm:p-5 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl hover:border-yellow-400/30 hover:bg-yellow-400/5"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-yellow-400/10 to-yellow-500/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-2 sm:gap-3">
              <div className="p-2 sm:p-3 bg-yellow-400/10 rounded-lg group-hover:bg-yellow-400/20 transition-colors duration-300">
                <Star className="h-5 w-5 sm:h-6 sm:w-6 text-yellow-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xs sm:text-sm font-medium text-center group-hover:text-yellow-600 transition-colors duration-300">
                Starred
              </span>
            </div>
          </div>

          <div
            onClick={() => {
              /* Search functionality */
            }}
            className="group relative bg-background/60 backdrop-blur-sm border border-border/50 rounded-xl p-4 sm:p-5 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl hover:border-green-500/30 hover:bg-green-500/5"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-green-500/10 to-green-600/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-2 sm:gap-3">
              <div className="p-2 sm:p-3 bg-green-500/10 rounded-lg group-hover:bg-green-500/20 transition-colors duration-300">
                <Search className="h-5 w-5 sm:h-6 sm:w-6 text-green-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xs sm:text-sm font-medium text-center group-hover:text-green-600 transition-colors duration-300">
                Search
              </span>
            </div>
          </div>

          <div
            onClick={() => {
              /* Templates functionality */
            }}
            className="group relative bg-background/60 backdrop-blur-sm border border-border/50 rounded-xl p-4 sm:p-5 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl hover:border-purple-500/30 hover:bg-purple-500/5"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-purple-500/10 to-purple-600/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-2 sm:gap-3">
              <div className="p-2 sm:p-3 bg-purple-500/10 rounded-lg group-hover:bg-purple-500/20 transition-colors duration-300">
                <BookOpen className="h-5 w-5 sm:h-6 sm:w-6 text-purple-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xs sm:text-sm font-medium text-center group-hover:text-purple-600 transition-colors duration-300">
                Templates
              </span>
            </div>
          </div>

          <div
            onClick={() => {
              /* Tags functionality */
            }}
            className="group relative bg-background/60 backdrop-blur-sm border border-border/50 rounded-xl p-4 sm:p-5 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-xl hover:border-orange-500/30 hover:bg-orange-500/5 col-span-2 sm:col-span-1"
          >
            <div className="absolute inset-0 bg-gradient-to-br from-orange-500/10 to-orange-600/5 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            <div className="relative flex flex-col items-center gap-2 sm:gap-3">
              <div className="p-2 sm:p-3 bg-orange-500/10 rounded-lg group-hover:bg-orange-500/20 transition-colors duration-300">
                <Tag className="h-5 w-5 sm:h-6 sm:w-6 text-orange-600 group-hover:scale-110 transition-transform duration-300" />
              </div>
              <span className="text-xs sm:text-sm font-medium text-center group-hover:text-orange-600 transition-colors duration-300">
                Tags
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
