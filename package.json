{"name": "note_flow", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/mongodb-adapter": "^3.10.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "^1.3.7", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@tiptap/extension-bubble-menu": "^2.25.0", "@tiptap/extension-character-count": "^2.25.0", "@tiptap/extension-collaboration": "^2.25.0", "@tiptap/extension-collaboration-cursor": "^2.25.0", "@tiptap/extension-color": "^2.25.0", "@tiptap/extension-dropcursor": "^2.25.0", "@tiptap/extension-floating-menu": "^2.25.0", "@tiptap/extension-focus": "^2.25.0", "@tiptap/extension-font-family": "^2.25.0", "@tiptap/extension-gapcursor": "^2.25.0", "@tiptap/extension-hard-break": "^2.25.0", "@tiptap/extension-highlight": "^2.25.0", "@tiptap/extension-horizontal-rule": "^2.25.0", "@tiptap/extension-image": "^2.25.0", "@tiptap/extension-link": "^2.25.0", "@tiptap/extension-mention": "^2.25.0", "@tiptap/extension-placeholder": "^2.25.0", "@tiptap/extension-subscript": "^2.25.0", "@tiptap/extension-superscript": "^2.25.0", "@tiptap/extension-table": "^2.25.0", "@tiptap/extension-table-cell": "^2.25.0", "@tiptap/extension-table-header": "^2.25.0", "@tiptap/extension-table-row": "^2.25.0", "@tiptap/extension-task-item": "^2.25.0", "@tiptap/extension-task-list": "^2.25.0", "@tiptap/extension-text-align": "^2.25.0", "@tiptap/extension-text-style": "^2.25.0", "@tiptap/extension-typography": "^2.25.0", "@tiptap/extension-underline": "^2.25.0", "@tiptap/extension-youtube": "^2.25.0", "@tiptap/pm": "^2.25.0", "@tiptap/react": "^2.25.0", "@tiptap/starter-kit": "^2.25.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^17.1.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.525.0", "mongodb": "^6.17.0", "mongoose": "^8.16.1", "next": "15.3.5", "next-auth": "^5.0.0-beta.15", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "react": "^19.0.0", "react-dom": "^19.0.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/nodemailer": "^6.4.17", "eslint": "^9", "eslint-config-next": "15.3.5", "tailwindcss": "^4", "tw-animate-css": "^1.3.5"}}